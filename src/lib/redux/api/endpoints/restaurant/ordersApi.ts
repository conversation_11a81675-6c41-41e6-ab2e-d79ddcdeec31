import { apiSlice } from '../../apiSlice';

// Order-specific types
export interface Order {
  id: string;
  merchantId: string;
  customerId: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  items: OrderItem[];
  status: 'pending' | 'preparing' | 'ready' | 'delivered' | 'completed' | 'cancelled';
  total: number;
  subtotal: number;
  tax: number;
  tip: number;
  discount: number;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  orderType: 'dine-in' | 'takeout' | 'delivery';
  tableId?: string;
  deliveryAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  specialInstructions?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  price: number;
  quantity: number;
  options: OrderItemOption[];
  specialInstructions?: string;
}

export interface OrderItemOption {
  id: string;
  name: string;
  value: string;
  price: number;
}

// Create the orders API
export const ordersApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all orders for a branch
    getOrders: builder.query<Order[], {
      merchantId: string;
      branchId: string;
      status?: string;
      page?: number;
      limit?: number;
    }>({
      query: ({ merchantId, branchId, status, page = 1, limit = 20 }) => {
        const params = new URLSearchParams();
        if (status) params.append('status', status);
        params.append('page', page.toString());
        params.append('limit', limit.toString());

        return `/merchants/${merchantId}/branches/${branchId}/orders?${params.toString()}`;
      },
      providesTags: ['Orders'],
    }),

    // Get active orders (pending, preparing, ready)
    getActiveOrders: builder.query<Order[], { merchantId: string; branchId: string }>({
      query: ({ merchantId, branchId }) => `/merchants/${merchantId}/branches/${branchId}/orders/active`,
      providesTags: ['Orders'],
    }),

    // Get completed orders
    getCompletedOrders: builder.query<Order[], { merchantId: string; branchId: string }>({
      query: ({ merchantId, branchId }) => `/merchants/${merchantId}/branches/${branchId}/orders/completed`,
      providesTags: ['Orders'],
    }),

    // Get order by ID
    getOrderById: builder.query<Order, { merchantId: string; orderId: string }>({
      query: ({ merchantId, orderId }) => `/merchants/${merchantId}/orders/${orderId}`,
      providesTags: (result, error, arg) => [{ type: 'Orders', id: arg.orderId }],
    }),

    // Create a new order
    createOrder: builder.mutation<Order, { merchantId: string; order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> }>({
      query: ({ merchantId, order }) => ({
        url: `/merchants/${merchantId}/orders`,
        method: 'POST',
        body: order,
      }),
      invalidatesTags: ['Orders'],
    }),

    // Update an order
    updateOrder: builder.mutation<Order, { merchantId: string; orderId: string; order: Partial<Order> }>({
      query: ({ merchantId, orderId, order }) => ({
        url: `/merchants/${merchantId}/orders/${orderId}`,
        method: 'PUT',
        body: order,
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Update order status
    updateOrderStatus: builder.mutation<Order, { merchantId: string; orderId: string; status: Order['status'] }>({
      query: ({ merchantId, orderId, status }) => ({
        url: `/merchants/${merchantId}/orders/${orderId}/status`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Cancel an order
    cancelOrder: builder.mutation<Order, { merchantId: string; orderId: string; reason?: string }>({
      query: ({ merchantId, orderId, reason }) => ({
        url: `/merchants/${merchantId}/orders/${orderId}/cancel`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Process payment for an order
    processPayment: builder.mutation<{ success: boolean; transactionId?: string }, { merchantId: string; orderId: string; paymentDetails: any }>({
      query: ({ merchantId, orderId, paymentDetails }) => ({
        url: `/merchants/${merchantId}/orders/${orderId}/payment`,
        method: 'POST',
        body: paymentDetails,
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Issue refund for an order
    issueRefund: builder.mutation<{ success: boolean; refundId?: string }, { merchantId: string; orderId: string; amount?: number; reason?: string }>({
      query: ({ merchantId, orderId, amount, reason }) => ({
        url: `/merchants/${merchantId}/orders/${orderId}/refund`,
        method: 'POST',
        body: { amount, reason },
      }),
      invalidatesTags: (result, error, arg) => [
        'Orders',
        { type: 'Orders', id: arg.orderId },
      ],
    }),

    // Get order analytics
    getOrderAnalytics: builder.query<any, { merchantId: string; startDate?: string; endDate?: string }>({
      query: ({ merchantId, startDate, endDate }) => {
        let url = `/merchants/${merchantId}/orders/analytics`;
        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['OrderAnalytics'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetOrdersQuery,
  useGetActiveOrdersQuery,
  useGetCompletedOrdersQuery,
  useGetOrderByIdQuery,
  useCreateOrderMutation,
  useUpdateOrderMutation,
  useUpdateOrderStatusMutation,
  useCancelOrderMutation,
  useProcessPaymentMutation,
  useIssueRefundMutation,
  useGetOrderAnalyticsQuery,
} = ordersApi;
