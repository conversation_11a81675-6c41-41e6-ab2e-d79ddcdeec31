'use client';

import React, { use, useState } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Search, Clock, CheckCircle, XCircle, Eye, RefreshCw } from 'lucide-react';
import { useOrders } from '@/hooks/useOrders';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';

interface OrdersPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function OrdersPage({ params }: OrdersPageProps) {
  const { slugShop, slugBranch } = use(params);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  // Get merchant and branch data from backend
  const { data: merchantsData, isLoading: isLoadingMerchants } = useGetMerchantsQuery({});

  // Find the merchant by slug
  const merchant = merchantsData?.data?.find(m => m.slug === slugShop);
  const branch = merchant?.branches?.find(b => b.slug === slugBranch);

  const merchantId = merchant?.id;
  const branchId = branch?.id;

  const {
    orders,
    activeOrders,
    completedOrders,
    isLoading,
    isLoadingActive,
    isLoadingCompleted,
    getStatusColor,
    getStatusText,
    formatCurrency,
    refetch,
    refetchActive,
    refetchCompleted,
  } = useOrders({
    merchantId,
    branchId,
    status: activeTab === 'active' ? undefined : 'completed'
  });

  if (isLoadingMerchants || isLoading || isLoadingActive || isLoadingCompleted) {
    return <AppLoading />;
  }

  if (!merchant || !branch) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter orders based on search term
  const filterOrders = (ordersList: any[]) => {
    return ordersList.filter(order =>
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.items?.some((item: any) => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const filteredActiveOrders = filterOrders(activeOrders);
  const filteredCompletedOrders = filterOrders(completedOrders);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'preparing':
        return <Clock className="h-4 w-4" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <XCircle className="h-4 w-4" />;
    }
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const orderTime = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Orders Management</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage orders for your restaurant
          </p>
        </div>
        <div className="flex items-center">
          <Button
            variant="outline"
            onClick={() => {
              refetchActive();
              refetchCompleted();
            }}
            className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="bg-[#f1edea] p-1">
            <TabsTrigger value="active" className="data-[state=active]:bg-white">
              Active Orders ({filteredActiveOrders.length})
            </TabsTrigger>
            <TabsTrigger value="completed" className="data-[state=active]:bg-white">
              Completed Orders ({filteredCompletedOrders.length})
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 max-w-sm ml-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        <TabsContent value="active" className="mt-0">
          {filteredActiveOrders.length === 0 ? (
            <div className="flex items-center justify-center h-64 text-[#8a745c]">
              {searchTerm ? 'No active orders match your search' : 'No active orders'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredActiveOrders.map((order) => (
                <Card key={order.id} className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
                          {order.id}
                          <Badge className={getStatusColor(order.status)}>
                            {getStatusIcon(order.status)}
                            <span className="ml-1 capitalize">{order.status}</span>
                          </Badge>
                        </CardTitle>
                        <p className="text-[#8a745c] text-sm mt-1">
                          {order.customer?.name || 'Unknown Customer'} • {order.customer?.phone || 'No phone'}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-[#181510] font-bold">{formatCurrency(order.total)}</p>
                        <p className="text-[#8a745c] text-sm">{getTimeAgo(order.createdAt)}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4] capitalize">
                            {order.orderType || order.type || 'dine-in'}
                          </Badge>
                          {order.tableId && (
                            <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4]">
                              {order.tableId}
                            </Badge>
                          )}
                        </div>
                        {order.estimatedTime && (
                          <div className="flex items-center gap-1 text-[#8a745c] text-sm">
                            <Clock className="h-4 w-4" />
                            {order.estimatedTime} min
                          </div>
                        )}
                      </div>

                      <div className="space-y-1">
                        {order.items.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-[#181510]">{item.quantity}x {item.name}</span>
                            <span className="text-[#8a745c]">{formatCurrency(item.total || (item.price * item.quantity))}</span>
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-end pt-2">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id.replace('#', '')}`}>
                          <Button variant="outline" size="sm" className="border-[#e2dcd4]">
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed" className="mt-0">
          {filteredCompletedOrders.length === 0 ? (
            <div className="flex items-center justify-center h-64 text-[#8a745c]">
              {searchTerm ? 'No completed orders match your search' : 'No completed orders'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCompletedOrders.map((order) => (
                <Card key={order.id} className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
                          {order.id}
                          <Badge className={getStatusColor(order.status)}>
                            {getStatusIcon(order.status)}
                            <span className="ml-1 capitalize">{order.status}</span>
                          </Badge>
                        </CardTitle>
                        <p className="text-[#8a745c] text-sm mt-1">
                          {order.customer?.name || 'Unknown Customer'} • {order.customer?.phone || 'No phone'}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-[#181510] font-bold">{formatCurrency(order.total)}</p>
                        <p className="text-[#8a745c] text-sm">
                          Completed {getTimeAgo(order.completedAt || order.createdAt)}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4] capitalize">
                          {order.orderType || order.type || 'dine-in'}
                        </Badge>
                        {order.tableId && (
                          <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4]">
                            {order.tableId}
                          </Badge>
                        )}
                      </div>

                      <div className="space-y-1">
                        {order.items.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-[#181510]">{item.quantity}x {item.name}</span>
                            <span className="text-[#8a745c]">{formatCurrency(item.total || (item.price * item.quantity))}</span>
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-end pt-2">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id.replace('#', '')}`}>
                          <Button variant="outline" size="sm" className="border-[#e2dcd4]">
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
