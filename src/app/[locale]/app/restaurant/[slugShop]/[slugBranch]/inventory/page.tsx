'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  Package, 
  Plus, 
  Search, 
  Filter, 
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  Calendar,
  DollarSign,
  BarChart3,
  RefreshCw,
  Download,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  ShoppingCart
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types
interface InventoryItem {
  id: string;
  name: string;
  category: string;
  sku: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  totalValue: number;
  supplier: string;
  lastRestocked: Date;
  expiryDate?: Date;
  status: 'in-stock' | 'low-stock' | 'out-of-stock' | 'expired';
}

interface InventoryStats {
  totalItems: number;
  lowStockItems: number;
  outOfStockItems: number;
  totalValue: number;
  expiringSoon: number;
}

export default function InventoryPage() {
  const t = useTranslations('inventory');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data
  const mockItems: InventoryItem[] = [
    {
      id: '1',
      name: 'Tomatoes',
      category: 'Vegetables',
      sku: 'VEG-001',
      currentStock: 25,
      minStock: 10,
      maxStock: 50,
      unit: 'kg',
      costPerUnit: 3.50,
      totalValue: 87.50,
      supplier: 'Fresh Farm Co.',
      lastRestocked: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      expiryDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      status: 'in-stock',
    },
    {
      id: '2',
      name: 'Chicken Breast',
      category: 'Meat',
      sku: 'MEAT-001',
      currentStock: 5,
      minStock: 8,
      maxStock: 30,
      unit: 'kg',
      costPerUnit: 12.99,
      totalValue: 64.95,
      supplier: 'Premium Meats Ltd.',
      lastRestocked: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      status: 'low-stock',
    },
    {
      id: '3',
      name: 'Olive Oil',
      category: 'Oils & Condiments',
      sku: 'OIL-001',
      currentStock: 0,
      minStock: 5,
      maxStock: 20,
      unit: 'bottles',
      costPerUnit: 8.99,
      totalValue: 0,
      supplier: 'Mediterranean Imports',
      lastRestocked: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: 'out-of-stock',
    },
    {
      id: '4',
      name: 'Mozzarella Cheese',
      category: 'Dairy',
      sku: 'DAIRY-001',
      currentStock: 15,
      minStock: 5,
      maxStock: 25,
      unit: 'kg',
      costPerUnit: 9.50,
      totalValue: 142.50,
      supplier: 'Artisan Dairy Co.',
      lastRestocked: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      expiryDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),
      status: 'in-stock',
    },
    {
      id: '5',
      name: 'Flour',
      category: 'Baking',
      sku: 'BAK-001',
      currentStock: 45,
      minStock: 20,
      maxStock: 100,
      unit: 'kg',
      costPerUnit: 2.25,
      totalValue: 101.25,
      supplier: 'Grain Masters',
      lastRestocked: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      status: 'in-stock',
    },
  ];

  const mockStats: InventoryStats = {
    totalItems: mockItems.length,
    lowStockItems: mockItems.filter(item => item.status === 'low-stock').length,
    outOfStockItems: mockItems.filter(item => item.status === 'out-of-stock').length,
    totalValue: mockItems.reduce((sum, item) => sum + item.totalValue, 0),
    expiringSoon: mockItems.filter(item => 
      item.expiryDate && 
      item.expiryDate.getTime() - Date.now() < 3 * 24 * 60 * 60 * 1000
    ).length,
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setItems(mockItems);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter items
  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.supplier.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  // Get unique categories
  const categories = Array.from(new Set(items.map(item => item.category)));

  // Status badge styling
  const getStatusBadge = (status: InventoryItem['status']) => {
    const statusConfig = {
      'in-stock': { color: 'bg-green-100 text-green-800', icon: Package },
      'low-stock': { color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle },
      'out-of-stock': { color: 'bg-red-100 text-red-800', icon: TrendingDown },
      'expired': { color: 'bg-gray-100 text-gray-800', icon: Calendar },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Check if item is expiring soon
  const isExpiringSoon = (expiryDate?: Date) => {
    if (!expiryDate) return false;
    const daysUntilExpiry = (expiryDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24);
    return daysUntilExpiry <= 3 && daysUntilExpiry > 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Package className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
            <p className="text-gray-600">Track and manage your restaurant inventory</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/add`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Item
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalItems}</div>
            <p className="text-xs text-muted-foreground">
              Active inventory items
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{mockStats.lowStockItems}</div>
            <p className="text-xs text-muted-foreground">
              Items below minimum
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{mockStats.outOfStockItems}</div>
            <p className="text-xs text-muted-foreground">
              Items need restocking
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockStats.totalValue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Current inventory value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{mockStats.expiringSoon}</div>
            <p className="text-xs text-muted-foreground">
              Items expire in 3 days
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/low-stock`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <AlertTriangle className="h-8 w-8 text-yellow-600 mr-4" />
              <div>
                <h3 className="font-semibold">Low Stock Alert</h3>
                <p className="text-sm text-gray-600">{mockStats.lowStockItems} items need attention</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/purchase-orders`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <ShoppingCart className="h-8 w-8 text-blue-600 mr-4" />
              <div>
                <h3 className="font-semibold">Purchase Orders</h3>
                <p className="text-sm text-gray-600">Manage supplier orders</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/reports`}>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center p-6">
              <BarChart3 className="h-8 w-8 text-green-600 mr-4" />
              <div>
                <h3 className="font-semibold">Inventory Reports</h3>
                <p className="text-sm text-gray-600">View usage analytics</p>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search items..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="in-stock">In Stock</SelectItem>
            <SelectItem value="low-stock">Low Stock</SelectItem>
            <SelectItem value="out-of-stock">Out of Stock</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Inventory Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Stock</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Value</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Expiry</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-sm text-gray-500">SKU: {item.sku}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{item.category}</Badge>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {item.currentStock} {item.unit}
                    </div>
                    <div className="text-sm text-gray-500">
                      Min: {item.minStock} | Max: {item.maxStock}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(item.status)}</TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">${item.totalValue.toFixed(2)}</div>
                    <div className="text-sm text-gray-500">
                      ${item.costPerUnit.toFixed(2)}/{item.unit}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{item.supplier}</div>
                </TableCell>
                <TableCell>
                  {item.expiryDate ? (
                    <div className={cn(
                      "text-sm",
                      isExpiringSoon(item.expiryDate) ? "text-orange-600 font-medium" : "text-gray-600"
                    )}>
                      {formatDate(item.expiryDate)}
                      {isExpiringSoon(item.expiryDate) && (
                        <div className="text-xs text-orange-600">Expiring soon!</div>
                      )}
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/${item.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/${item.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Item
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Item
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No inventory items found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || categoryFilter !== 'all' || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start by adding your first inventory item'}
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory/items/add`}>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add First Item
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
