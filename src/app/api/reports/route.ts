/**
 * Enhanced Reports API Route with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import { serverFetchClient } from '@/lib/api/server-fetch-client';

export async function GET(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    // Validate required parameters
    if (!shopId || !branchId) {
      return NextResponse.json(
        { error: 'shopId and branchId are required' },
        { status: 400 }
      );
    }

    // Extract filtering, sorting, and pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sort_by = searchParams.get('sort_by') || 'created_at';
    const sort_order = searchParams.get('sort_order') || 'desc';

    // Filtering parameters
    const period = searchParams.get('period');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const category = searchParams.get('category');
    const report_type = searchParams.get('report_type');
    const search = searchParams.get('search');
    const date_range = searchParams.get('date_range');

    // Build query parameters for backend
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sort_by,
      sort_order,
    });

    // Add optional filters
    if (period) queryParams.append('period', period);
    if (startDate) queryParams.append('start_date', startDate);
    if (endDate) queryParams.append('end_date', endDate);
    if (category) queryParams.append('category', category);
    if (report_type) queryParams.append('report_type', report_type);
    if (search) queryParams.append('search', search);
    if (date_range) queryParams.append('date_range', date_range);

    // Construct backend URL
    const backendUrl = `/merchants/${shopId}/branches/${branchId}/reports?${queryParams.toString()}`;

    console.log('Fetching reports from backend:', {
      fullUrl: backendUrl,
      shopId,
      branchId,
      queryParams: queryParams.toString()
    });

    // Make request to backend
    const response = await serverFetchClient(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend reports API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Transform backend response to match frontend expectations
    const transformedResponse = {
      data: data.reports || [],
      total: data.total || 0,
      page: data.page || page,
      limit: data.limit || limit,
      totalPages: data.totalPages || Math.ceil((data.total || 0) / limit),
      summary: {
        totalReports: data.summary?.total || 0,
        totalRevenue: data.summary?.totalRevenue || 0,
        totalOrders: data.summary?.totalOrders || 0,
        averageOrderValue: data.summary?.averageOrderValue || 0,
        revenueGrowth: data.summary?.revenueGrowth || 0,
        ordersGrowth: data.summary?.ordersGrowth || 0,
      },
      // Include analytics data
      salesTrends: data.salesTrends || [],
      popularItems: data.popularItems || [],
      customerAnalytics: data.customerAnalytics || {
        totalCustomers: 0,
        newCustomers: 0,
        returningCustomers: 0,
        averageSpend: 0,
        repeatCustomerRate: 0,
        customerLifetimeValue: 0,
      },
      revenueAnalytics: data.revenueAnalytics || {
        totalRevenue: 0,
        revenueGrowth: 0,
        averageOrderValue: 0,
        totalOrders: 0,
        orderGrowth: 0,
      },
      period: data.period || period || 'month',
      generatedAt: data.generatedAt || new Date().toISOString(),
    };

    return NextResponse.json(transformedResponse);

  } catch (error) {
    console.error('Reports API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { type, filters, options } = body;

    // Validate required fields
    if (!type) {
      return NextResponse.json(
        { error: 'Report type is required' },
        { status: 400 }
      );
    }

    // Generate new report
    const newReport = {
      id: `${type}-${Date.now()}`,
      title: `${type.charAt(0).toUpperCase() + type.slice(1)} Report`,
      type,
      status: 'generating',
      generatedAt: new Date(),
      filters,
      options,
    };

    // In a real app, you would:
    // 1. Queue the report generation job
    // 2. Store the report request in database
    // 3. Return job ID for status tracking

    // Simulate report generation
    setTimeout(() => {
      newReport.status = 'ready';
    }, 5000);

    return NextResponse.json(
      {
        message: 'Report generation started',
        report: newReport
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Generate report error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
