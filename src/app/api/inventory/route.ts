/**
 * Enhanced Inventory API Route with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/nextauth.config';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';

export async function GET(request: NextRequest) {
  try {
    // Get session for authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    // Validate required parameters
    if (!shopId || !branchId) {
      return NextResponse.json(
        { error: 'shopId and branchId are required' },
        { status: 400 }
      );
    }

    // Extract filtering, sorting, and pagination parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const sort_by = searchParams.get('sort_by') || 'name';
    const sort_order = searchParams.get('sort_order') || 'asc';

    // Filtering parameters
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const supplier = searchParams.get('supplier');
    const search = searchParams.get('search');
    const lowStock = searchParams.get('lowStock');
    const outOfStock = searchParams.get('outOfStock');
    const expiringSoon = searchParams.get('expiringSoon');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const dateRange = searchParams.get('dateRange');

    // Build query parameters for backend
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sort_by,
      sort_order,
    });

    // Add optional filters
    if (category) queryParams.append('category', category);
    if (status) queryParams.append('status', status);
    if (supplier) queryParams.append('supplier', supplier);
    if (search) queryParams.append('search', search);
    if (lowStock) queryParams.append('low_stock', lowStock);
    if (outOfStock) queryParams.append('out_of_stock', outOfStock);
    if (expiringSoon) queryParams.append('expiring_soon', expiringSoon);
    if (startDate) queryParams.append('start_date', startDate);
    if (endDate) queryParams.append('end_date', endDate);
    if (dateRange) queryParams.append('date_range', dateRange);

    // Construct backend URL
    const backendUrl = `/merchants/${shopId}/branches/${branchId}/inventory?${queryParams.toString()}`;

    console.log('Fetching inventory from backend:', {
      fullUrl: backendUrl,
      shopId,
      branchId,
      queryParams: queryParams.toString()
    });

    // Make request to backend
    const response = await serverFetchClient(backendUrl, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend inventory API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Transform backend response to match frontend expectations
    const transformedResponse = {
      data: data.items || [],
      total: data.total || 0,
      page: data.page || page,
      limit: data.limit || limit,
      totalPages: data.totalPages || Math.ceil((data.total || 0) / limit),
      summary: {
        totalItems: data.summary?.total || 0,
        lowStockItems: data.summary?.lowStock || 0,
        outOfStockItems: data.summary?.outOfStock || 0,
        expiringSoonItems: data.summary?.expiringSoon || 0,
        totalValue: data.summary?.totalValue || 0,
        byCategory: data.summary?.byCategory || {},
        byStatus: data.summary?.byStatus || {},
        bySupplier: data.summary?.bySupplier || {},
      },
    };

    return NextResponse.json(transformedResponse);

  } catch (error) {
    console.error('Inventory API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    // Validate required parameters
    if (!shopId || !branchId) {
      return NextResponse.json(
        { error: 'shopId and branchId are required' },
        { status: 400 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { 
      name, 
      category, 
      sku, 
      description,
      currentStock, 
      minStock, 
      maxStock, 
      unit, 
      costPerUnit, 
      supplier,
      supplierContact,
      expiryDate,
      location,
      notes
    } = body;

    // Validate required fields
    if (!name || !category || !sku || currentStock === undefined || !unit || !supplier) {
      return NextResponse.json(
        { error: 'name, category, sku, currentStock, unit, and supplier are required' },
        { status: 400 }
      );
    }

    // Construct backend URL
    const backendUrl = `/merchants/${shopId}/branches/${branchId}/inventory`;

    console.log('Creating inventory item via backend:', {
      fullUrl: backendUrl,
      shopId,
      branchId,
      itemData: { name, category, sku, currentStock, unit, supplier }
    });

    // Make request to backend
    const response = await serverFetchClient(backendUrl, request, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        category,
        sku,
        description,
        currentStock,
        minStock: minStock || 0,
        maxStock: maxStock || 100,
        unit,
        costPerUnit: costPerUnit || 0,
        supplier,
        supplierContact,
        expiryDate,
        location,
        notes,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Backend create inventory item API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      return NextResponse.json(
        { error: `Backend API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    return NextResponse.json(data, { status: 201 });

  } catch (error) {
    console.error('Create inventory item API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
