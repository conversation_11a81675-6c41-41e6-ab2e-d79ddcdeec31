/**
 * API route for staff management
 * Forwards requests to the Golang backend with consistent filtering, sorting, and pagination
 */

import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';
import { handleApiResponse } from '@/lib/fetch/fetchClient';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shopId = searchParams.get('shopId');
    const branchId = searchParams.get('branchId');

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    // Construct the backend URL - using the branch-specific staff endpoint
    let url = `/merchants/${shopId}/branches/${branchId}/staff`;

    // Add any additional query parameters with defaults
    const otherParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'shopId' && key !== 'branchId') {
        otherParams.append(key, value);
      }
    });

    // Add default pagination parameters if not provided
    if (!otherParams.has('page')) {
      otherParams.append('page', '1');
    }
    if (!otherParams.has('limit')) {
      otherParams.append('limit', '20');
    }

    // Add default sorting if not provided
    if (!otherParams.has('sort_by')) {
      otherParams.append('sort_by', 'first_name');
    }
    if (!otherParams.has('sort_order')) {
      otherParams.append('sort_order', 'asc');
    }

    if (otherParams.toString()) {
      url += `?${otherParams.toString()}`;
    }

    console.log('Fetching staff from backend:', {
      fullUrl: url,
      shopId,
      branchId,
      queryParams: otherParams.toString()
    });

    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in GET /api/staff:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch staff' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shopId, branchId, ...staffData } = body;

    if (!shopId) {
      return NextResponse.json(
        { error: 'shopId is required' },
        { status: 400 }
      );
    }

    if (!branchId) {
      return NextResponse.json(
        { error: 'branchId is required' },
        { status: 400 }
      );
    }

    const url = `/merchants/${shopId}/branches/${branchId}/staff`;

    console.log('Creating staff member:', {
      url,
      shopId,
      branchId,
      data: staffData
    });

    const response = await serverFetchClient(url, request, {
      method: 'POST',
      body: JSON.stringify(staffData),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/staff:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create staff member' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
