import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Mock data - replace with database calls
const mockNotifications = [
  {
    id: '1',
    type: 'order',
    title: 'New Order Received',
    message: 'Order #1234 from <PERSON> - $45.99',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    read: false,
    priority: 'high',
    actionUrl: '/app/restaurant/orders/1234',
    actionLabel: 'View Order',
    metadata: { orderId: '1234', amount: 45.99 }
  },
  {
    id: '2',
    type: 'reservation',
    title: 'New Reservation',
    message: 'Table for 4 at 7:00 PM today - <PERSON>',
    timestamp: new Date(Date.now() - 15 * 60 * 1000),
    read: false,
    priority: 'medium',
    actionUrl: '/app/restaurant/reservations',
    actionLabel: 'View Reservation'
  },
  {
    id: '3',
    type: 'review',
    title: 'New Review',
    message: '5-star review: "Amazing food and service!"',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    read: true,
    priority: 'low',
    actionUrl: '/app/restaurant/reviews',
    actionLabel: 'View Review'
  },
  {
    id: '4',
    type: 'warning',
    title: 'Low Stock Alert',
    message: 'Tomatoes are running low (5 units remaining)',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: false,
    priority: 'urgent',
    actionUrl: '/app/restaurant/inventory',
    actionLabel: 'Manage Inventory'
  },
  {
    id: '5',
    type: 'system',
    title: 'System Update',
    message: 'New features available in your dashboard',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    read: true,
    priority: 'low',
    actionUrl: '/app/restaurant/settings',
    actionLabel: 'View Updates'
  }
];

export async function GET(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const filter = searchParams.get('filter') || 'all';
    const unreadOnly = searchParams.get('unread') === 'true';

    // Filter notifications
    let filteredNotifications = [...mockNotifications];

    if (unreadOnly) {
      filteredNotifications = filteredNotifications.filter(n => !n.read);
    }

    if (filter !== 'all') {
      filteredNotifications = filteredNotifications.filter(n => n.type === filter);
    }

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedNotifications = filteredNotifications.slice(startIndex, endIndex);

    // Calculate counts
    const totalCount = filteredNotifications.length;
    const unreadCount = mockNotifications.filter(n => !n.read).length;

    return NextResponse.json({
      notifications: paginatedNotifications,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
      counts: {
        total: mockNotifications.length,
        unread: unreadCount,
        urgent: mockNotifications.filter(n => n.priority === 'urgent' && !n.read).length,
      },
    });

  } catch (error) {
    console.error('Notifications API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get session
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate required fields
    if (!body.type || !body.title || !body.message) {
      return NextResponse.json(
        { error: 'Missing required fields: type, title, message' },
        { status: 400 }
      );
    }

    // Create new notification
    const newNotification = {
      id: Date.now().toString(),
      type: body.type,
      title: body.title,
      message: body.message,
      timestamp: new Date(),
      read: false,
      priority: body.priority || 'medium',
      actionUrl: body.actionUrl,
      actionLabel: body.actionLabel,
      metadata: body.metadata || {},
    };

    // In a real app, save to database
    mockNotifications.unshift(newNotification);

    // TODO: Send real-time notification via WebSocket
    // TODO: Send push notification if enabled
    // TODO: Send email notification if enabled

    return NextResponse.json(
      { notification: newNotification },
      { status: 201 }
    );

  } catch (error) {
    console.error('Create notification error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
