/**
 * Custom hook for reports and analytics
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetBranchReportsQuery,
  useGetSalesTrendsQuery,
  useGetPopularItemsQuery,
  useGetCustomerAnalyticsQuery,
  useGetRevenueAnalyticsQuery,
  useGetDashboardStatsQuery,
  useExportReportsMutation,
  useGetRealTimeMetricsQuery,
  type ReportsFilters,
} from '@/lib/redux/api/endpoints/restaurant/reportsApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseReportsOptions {
  merchantId: string;
  branchId: string;
  initialFilters?: ReportsFilters;
}

export function useReports({
  merchantId,
  branchId,
  initialFilters = {}
}: UseReportsOptions) {
  const [filters, setFilters] = useState<ReportsFilters>({
    period: 'month',
    ...initialFilters
  });

  // API queries
  const {
    data: reportsData,
    isLoading: isLoadingReports,
    isError: isReportsError,
    error: reportsError,
    refetch: refetchReports
  } = useGetBranchReportsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: salesTrends,
    isLoading: isLoadingSales,
    refetch: refetchSales
  } = useGetSalesTrendsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: popularItems,
    isLoading: isLoadingItems,
    refetch: refetchItems
  } = useGetPopularItemsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: customerAnalytics,
    isLoading: isLoadingCustomers,
    refetch: refetchCustomers
  } = useGetCustomerAnalyticsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: revenueAnalytics,
    isLoading: isLoadingRevenue,
    refetch: refetchRevenue
  } = useGetRevenueAnalyticsQuery({
    merchantId,
    branchId,
    filters
  });

  const {
    data: dashboardStats,
    isLoading: isLoadingDashboard,
    refetch: refetchDashboard
  } = useGetDashboardStatsQuery({
    merchantId,
    branchId
  });

  const {
    data: realTimeMetrics,
    isLoading: isLoadingRealTime,
    refetch: refetchRealTime
  } = useGetRealTimeMetricsQuery({
    merchantId,
    branchId
  });

  // Mutations
  const [exportReports, { isLoading: isExporting }] = useExportReportsMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<ReportsFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ period: 'month' });
  }, []);

  // Export functionality
  const handleExportReports = useCallback(async (format: 'pdf' | 'excel' | 'csv') => {
    try {
      const result = await exportReports({
        merchantId,
        branchId,
        format,
        filters
      }).unwrap();
      
      // Download the file
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = `reports-${branchId}-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      errorHandlers.showSuccessToast(`Reports exported as ${format.toUpperCase()}`);
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [exportReports, merchantId, branchId, filters]);

  // Refresh all data
  const refreshAllData = useCallback(() => {
    refetchReports();
    refetchSales();
    refetchItems();
    refetchCustomers();
    refetchRevenue();
    refetchDashboard();
    refetchRealTime();
  }, [
    refetchReports,
    refetchSales,
    refetchItems,
    refetchCustomers,
    refetchRevenue,
    refetchDashboard,
    refetchRealTime
  ]);

  // Computed values
  const isLoading = useMemo(() => 
    isLoadingReports || isLoadingSales || isLoadingItems || 
    isLoadingCustomers || isLoadingRevenue || isLoadingDashboard,
    [isLoadingReports, isLoadingSales, isLoadingItems, isLoadingCustomers, isLoadingRevenue, isLoadingDashboard]
  );

  // Format chart data for different periods
  const formatChartData = useCallback((data: any[], period: string) => {
    if (!data) return [];
    
    return data.map(item => ({
      ...item,
      name: formatPeriodLabel(item.period, period),
      formattedSales: `$${item.sales.toLocaleString()}`,
      formattedOrders: item.orders.toLocaleString()
    }));
  }, []);

  const formatPeriodLabel = useCallback((period: string, type: string) => {
    const date = new Date(period);
    
    switch (type) {
      case 'day':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case 'week':
        return `Week ${Math.ceil(date.getDate() / 7)}`;
      case 'month':
        return date.toLocaleDateString('en-US', { month: 'short' });
      case 'quarter':
        return `Q${Math.ceil((date.getMonth() + 1) / 3)}`;
      case 'year':
        return date.getFullYear().toString();
      default:
        return period;
    }
  }, []);

  // Calculate growth percentages
  const calculateGrowth = useCallback((current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }, []);

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }, []);

  // Format percentage
  const formatPercentage = useCallback((value: number, decimals = 1) => {
    return `${value.toFixed(decimals)}%`;
  }, []);

  // Get period options for filters
  const periodOptions = useMemo(() => [
    { value: 'day', label: 'Daily' },
    { value: 'week', label: 'Weekly' },
    { value: 'month', label: 'Monthly' },
    { value: 'quarter', label: 'Quarterly' },
    { value: 'year', label: 'Yearly' }
  ], []);

  return {
    // Data
    reportsData,
    salesTrends: formatChartData(salesTrends, filters.period || 'month'),
    popularItems,
    customerAnalytics,
    revenueAnalytics,
    dashboardStats,
    realTimeMetrics,
    
    // Loading states
    isLoading,
    isLoadingReports,
    isLoadingSales,
    isLoadingItems,
    isLoadingCustomers,
    isLoadingRevenue,
    isLoadingDashboard,
    isLoadingRealTime,
    isExporting,
    
    // Error states
    isReportsError,
    reportsError,
    
    // Filters
    filters,
    updateFilters,
    clearFilters,
    periodOptions,
    
    // Actions
    exportReports: handleExportReports,
    refreshAllData,
    
    // Utilities
    calculateGrowth,
    formatCurrency,
    formatPercentage,
    formatPeriodLabel,
  };
}

// Hook for dashboard-specific data
export function useDashboardReports(merchantId: string, branchId: string) {
  const {
    data: dashboardStats,
    isLoading,
    refetch
  } = useGetDashboardStatsQuery({
    merchantId,
    branchId
  });

  const {
    data: realTimeMetrics,
    isLoading: isLoadingRealTime,
    refetch: refetchRealTime
  } = useGetRealTimeMetricsQuery({
    merchantId,
    branchId
  });

  return {
    dashboardStats,
    realTimeMetrics,
    isLoading: isLoading || isLoadingRealTime,
    refetch: () => {
      refetch();
      refetchRealTime();
    }
  };
}
