import { useState, useEffect, useCallback, useRef } from 'react';

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'order' | 'reservation' | 'review' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
  actionLabel?: string;
  metadata?: Record<string, any>;
}

export interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  soundEnabled: boolean;
  doNotDisturb: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

export interface UseNotificationsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealtime?: boolean;
  onNewNotification?: (notification: Notification) => void;
  onError?: (error: Error) => void;
}

export function useNotifications({
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
  enableRealtime = true,
  onNewNotification,
  onError,
}: UseNotificationsOptions = {}) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    soundEnabled: true,
    doNotDisturb: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
    },
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Fetch notifications from API
  const fetchNotifications = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch('/api/notifications');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setNotifications(data.notifications || []);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch notifications');
      setError(error.message);
      if (onError) {
        onError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [onError]);

  // Mark notification as read
  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'PATCH',
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as read');
      }

      setNotifications(prev => 
        prev.map(n => n.id === id ? { ...n, read: true } : n)
      );
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to mark as read');
      if (onError) {
        onError(error);
      }
    }
  }, [onError]);

  // Mark notification as unread
  const markAsUnread = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}/unread`, {
        method: 'PATCH',
      });

      if (!response.ok) {
        throw new Error('Failed to mark notification as unread');
      }

      setNotifications(prev => 
        prev.map(n => n.id === id ? { ...n, read: false } : n)
      );
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to mark as unread');
      if (onError) {
        onError(error);
      }
    }
  }, [onError]);

  // Delete notification
  const deleteNotification = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete notification');
      }

      setNotifications(prev => prev.filter(n => n.id !== id));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete notification');
      if (onError) {
        onError(error);
      }
    }
  }, [onError]);

  // Mark all as read
  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'PATCH',
      });

      if (!response.ok) {
        throw new Error('Failed to mark all notifications as read');
      }

      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to mark all as read');
      if (onError) {
        onError(error);
      }
    }
  }, [onError]);

  // Delete multiple notifications
  const deleteMultiple = useCallback(async (ids: string[]) => {
    try {
      const response = await fetch('/api/notifications/bulk-delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete notifications');
      }

      setNotifications(prev => prev.filter(n => !ids.includes(n.id)));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete notifications');
      if (onError) {
        onError(error);
      }
    }
  }, [onError]);

  // Add new notification (for real-time updates)
  const addNotification = useCallback((notification: Notification) => {
    setNotifications(prev => [notification, ...prev]);
    
    // Play sound if enabled
    if (settings.soundEnabled && !isDoNotDisturbActive()) {
      playNotificationSound();
    }

    // Call callback
    if (onNewNotification) {
      onNewNotification(notification);
    }
  }, [settings.soundEnabled, onNewNotification]);

  // Check if Do Not Disturb is active
  const isDoNotDisturbActive = useCallback(() => {
    if (!settings.doNotDisturb.enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = settings.doNotDisturb.startTime.split(':').map(Number);
    const [endHour, endMin] = settings.doNotDisturb.endTime.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Crosses midnight
      return currentTime >= startTime || currentTime <= endTime;
    }
  }, [settings.doNotDisturb]);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = 0.5;
      audio.play().catch(() => {
        // Ignore errors (user might not have interacted with page yet)
      });
    } catch (err) {
      // Ignore audio errors
    }
  }, []);

  // Update settings
  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>) => {
    try {
      const response = await fetch('/api/notifications/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });

      if (!response.ok) {
        throw new Error('Failed to update settings');
      }

      setSettings(prev => ({ ...prev, ...newSettings }));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update settings');
      if (onError) {
        onError(error);
      }
    }
  }, [onError]);

  // Setup WebSocket for real-time notifications
  useEffect(() => {
    if (!enableRealtime) return;

    const connectWebSocket = () => {
      try {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/api/notifications/ws`;
        
        wsRef.current = new WebSocket(wsUrl);

        wsRef.current.onopen = () => {
          console.log('Notification WebSocket connected');
        };

        wsRef.current.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (data.type === 'notification') {
              addNotification(data.notification);
            }
          } catch (err) {
            console.error('Failed to parse WebSocket message:', err);
          }
        };

        wsRef.current.onclose = () => {
          console.log('Notification WebSocket disconnected');
          // Reconnect after 5 seconds
          setTimeout(connectWebSocket, 5000);
        };

        wsRef.current.onerror = (error) => {
          console.error('WebSocket error:', error);
        };
      } catch (err) {
        console.error('Failed to connect WebSocket:', err);
      }
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [enableRealtime, addNotification]);

  // Setup auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    intervalRef.current = setInterval(fetchNotifications, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, fetchNotifications]);

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Computed values
  const unreadCount = notifications.filter(n => !n.read).length;
  const urgentCount = notifications.filter(n => n.priority === 'urgent' && !n.read).length;

  return {
    notifications,
    loading,
    error,
    settings,
    unreadCount,
    urgentCount,
    markAsRead,
    markAsUnread,
    deleteNotification,
    markAllAsRead,
    deleteMultiple,
    addNotification,
    updateSettings,
    refresh: fetchNotifications,
    isDoNotDisturbActive: isDoNotDisturbActive(),
  };
}
