/**
 * Custom hook for orders management
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetOrdersQuery,
  useGetActiveOrdersQuery,
  useGetCompletedOrdersQuery,
  useGetOrderByIdQuery,
  useCreateOrderMutation,
  useUpdateOrderMutation,
  useUpdateOrderStatusMutation,
  useCancelOrderMutation,
  useProcessPaymentMutation,
  useGetOrderAnalyticsQuery,
  type Order,
} from '@/lib/redux/api/endpoints/restaurant/ordersApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseOrdersOptions {
  merchantId: string;
  branchId: string;
  status?: string;
  page?: number;
  limit?: number;
}

export function useOrders({
  merchantId,
  branchId,
  status,
  page = 1,
  limit = 20
}: UseOrdersOptions) {
  const [localFilters, setLocalFilters] = useState({
    status,
    page,
    limit
  });

  // API queries
  const {
    data: orders,
    isLoading,
    isError,
    error,
    refetch
  } = useGetOrdersQuery({
    merchantId,
    branchId,
    ...localFilters
  });

  const {
    data: activeOrders,
    isLoading: isLoadingActive,
    refetch: refetchActive
  } = useGetActiveOrdersQuery({
    merchantId,
    branchId
  });

  const {
    data: completedOrders,
    isLoading: isLoadingCompleted,
    refetch: refetchCompleted
  } = useGetCompletedOrdersQuery({
    merchantId,
    branchId
  });

  // Mutations
  const [createOrder, { isLoading: isCreating }] = useCreateOrderMutation();
  const [updateOrder, { isLoading: isUpdating }] = useUpdateOrderMutation();
  const [updateOrderStatus, { isLoading: isUpdatingStatus }] = useUpdateOrderStatusMutation();
  const [cancelOrder, { isLoading: isCancelling }] = useCancelOrderMutation();
  const [processPayment, { isLoading: isProcessingPayment }] = useProcessPaymentMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<typeof localFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setLocalFilters({ page: 1, limit: 20 });
  }, []);

  // Order actions
  const handleCreateOrder = useCallback(async (orderData: any) => {
    try {
      await createOrder({
        merchantId,
        branchId,
        order: orderData
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CREATED);
      refetch();
      refetchActive();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createOrder, merchantId, branchId, refetch, refetchActive]);

  const handleUpdateOrder = useCallback(async (orderId: string, updates: any) => {
    try {
      await updateOrder({
        merchantId,
        branchId,
        orderId,
        order: updates
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
      refetchActive();
      refetchCompleted();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateOrder, merchantId, branchId, refetch, refetchActive, refetchCompleted]);

  const handleUpdateOrderStatus = useCallback(async (orderId: string, status: Order['status']) => {
    try {
      await updateOrderStatus({
        merchantId,
        branchId,
        orderId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Order ${status} successfully`);
      refetch();
      refetchActive();
      refetchCompleted();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateOrderStatus, merchantId, branchId, refetch, refetchActive, refetchCompleted]);

  const handleCancelOrder = useCallback(async (orderId: string, reason?: string) => {
    try {
      await cancelOrder({
        merchantId,
        branchId,
        orderId,
        reason
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CANCELLED);
      refetch();
      refetchActive();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [cancelOrder, merchantId, branchId, refetch, refetchActive]);

  const handleProcessPayment = useCallback(async (orderId: string, paymentDetails: any) => {
    try {
      const result = await processPayment({
        merchantId,
        branchId,
        orderId,
        paymentDetails
      }).unwrap();
      
      if (result.success) {
        errorHandlers.showSuccessToast('Payment processed successfully');
        refetch();
        refetchActive();
        refetchCompleted();
      } else {
        throw new Error('Payment failed');
      }
      
      return result;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [processPayment, merchantId, branchId, refetch, refetchActive, refetchCompleted]);

  // Status helpers
  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'delivered':
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  }, []);

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }, []);

  // Calculate order totals
  const orderTotals = useMemo(() => {
    if (!orders) return { total: 0, subtotal: 0, tax: 0 };
    
    return orders.reduce((acc, order) => ({
      total: acc.total + order.total,
      subtotal: acc.subtotal + order.subtotal,
      tax: acc.tax + order.tax
    }), { total: 0, subtotal: 0, tax: 0 });
  }, [orders]);

  // Status counts
  const statusCounts = useMemo(() => {
    if (!orders) return {};
    
    return orders.reduce((acc, order) => {
      const status = order.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [orders]);

  return {
    // Data
    orders: orders || [],
    activeOrders: activeOrders || [],
    completedOrders: completedOrders || [],
    orderTotals,
    statusCounts,
    
    // Loading states
    isLoading,
    isLoadingActive,
    isLoadingCompleted,
    isCreating,
    isUpdating,
    isUpdatingStatus,
    isCancelling,
    isProcessingPayment,
    isError,
    error,
    
    // Filters
    filters: localFilters,
    updateFilters,
    clearFilters,
    
    // Actions
    createOrder: handleCreateOrder,
    updateOrder: handleUpdateOrder,
    updateOrderStatus: handleUpdateOrderStatus,
    cancelOrder: handleCancelOrder,
    processPayment: handleProcessPayment,
    refetch,
    refetchActive,
    refetchCompleted,
    
    // Helpers
    getStatusColor,
    getStatusText,
    formatCurrency,
  };
}

// Hook for single order
export function useOrder(merchantId: string, branchId: string, orderId: string) {
  const {
    data: order,
    isLoading,
    isError,
    error,
    refetch
  } = useGetOrderByIdQuery({
    merchantId,
    branchId,
    orderId
  });

  const [updateOrderStatus, { isLoading: isUpdatingStatus }] = useUpdateOrderStatusMutation();
  const [cancelOrder, { isLoading: isCancelling }] = useCancelOrderMutation();
  const [processPayment, { isLoading: isProcessingPayment }] = useProcessPaymentMutation();

  const handleUpdateStatus = useCallback(async (status: Order['status']) => {
    try {
      await updateOrderStatus({
        merchantId,
        branchId,
        orderId,
        status
      }).unwrap();
      
      errorHandlers.showSuccessToast(`Order ${status} successfully`);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateOrderStatus, merchantId, branchId, orderId, refetch]);

  const handleCancel = useCallback(async (reason?: string) => {
    try {
      await cancelOrder({
        merchantId,
        branchId,
        orderId,
        reason
      }).unwrap();
      
      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CANCELLED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [cancelOrder, merchantId, branchId, orderId, refetch]);

  const handleProcessPayment = useCallback(async (paymentDetails: any) => {
    try {
      const result = await processPayment({
        merchantId,
        branchId,
        orderId,
        paymentDetails
      }).unwrap();
      
      if (result.success) {
        errorHandlers.showSuccessToast('Payment processed successfully');
        refetch();
      } else {
        throw new Error('Payment failed');
      }
      
      return result;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [processPayment, merchantId, branchId, orderId, refetch]);

  return {
    order,
    isLoading,
    isUpdatingStatus,
    isCancelling,
    isProcessingPayment,
    isError,
    error,
    updateStatus: handleUpdateStatus,
    cancelOrder: handleCancel,
    processPayment: handleProcessPayment,
    refetch,
  };
}
