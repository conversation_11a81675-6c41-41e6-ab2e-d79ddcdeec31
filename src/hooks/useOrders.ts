/**
 * Enhanced useOrders hook with backend-driven filtering, sorting, and pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { useState, useMemo } from 'react';
import {
  useGetOrdersQuery,
  OrderFilters,
  OrdersResponse,
  Order
} from '@/lib/redux/api/endpoints/restaurant/ordersApi';

export interface UseOrdersOptions {
  shopId: string;
  branchId: string;
  initialFilters?: OrderFilters;
}

export interface OrderStats {
  totalOrders: number;
  activeOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
}

export function useOrders({
  shopId,
  branchId,
  initialFilters = {}
}: UseOrdersOptions) {
  const [filters, setFilters] = useState<OrderFilters>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc',
    ...initialFilters
  });

  // API query
  const {
    data: ordersData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetOrdersQuery({
    shopId,
    branchId,
    filters
  });

  // Data processing
  const orders = ordersData?.data || [];
  const pagination = ordersData ? {
    currentPage: ordersData.page,
    totalPages: ordersData.total_pages,
    totalItems: ordersData.total,
    itemsPerPage: ordersData.limit,
    hasNextPage: ordersData.page < ordersData.total_pages,
    hasPreviousPage: ordersData.page > 1,
  } : undefined;

  // Statistics - calculated from backend total and current page data
  const orderStats = useMemo((): OrderStats => {
    const totalOrders = ordersData?.total || 0;
    const activeStatuses = ['pending', 'preparing', 'ready'];
    const completedStatuses = ['completed', 'delivered'];
    const cancelledStatuses = ['cancelled'];

    const activeOrders = orders.filter(order => activeStatuses.includes(order.status)).length;
    const completedOrders = orders.filter(order => completedStatuses.includes(order.status)).length;
    const cancelledOrders = orders.filter(order => cancelledStatuses.includes(order.status)).length;

    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    return {
      totalOrders,
      activeOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      averageOrderValue,
    };
  }, [orders, ordersData?.total]);

  // Filter management functions
  const updateFilters = (newFilters: Partial<OrderFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  };

  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
    });
  };

  const setSearch = (search: string) => {
    updateFilters({
      search: search || undefined,
      page: 1 // Reset to first page when searching
    });
  };

  const setStatus = (status: OrderFilters['status']) => {
    updateFilters({
      status: status || undefined,
      page: 1 // Reset to first page when filtering
    });
  };

  const setOrderType = (order_type: OrderFilters['order_type']) => {
    updateFilters({
      order_type: order_type || undefined,
      page: 1 // Reset to first page when filtering
    });
  };

  const setDateRange = (date_from?: string, date_to?: string) => {
    updateFilters({
      date_from: date_from || undefined,
      date_to: date_to || undefined,
      page: 1 // Reset to first page when filtering
    });
  };

  const setAmountRange = (min_amount?: number, max_amount?: number) => {
    updateFilters({
      min_amount: min_amount || undefined,
      max_amount: max_amount || undefined,
      page: 1 // Reset to first page when filtering
    });
  };

  const setSorting = (sort_by: OrderFilters['sort_by'], sort_order: OrderFilters['sort_order'] = 'asc') => {
    updateFilters({
      sort_by,
      sort_order,
      page: 1 // Reset to first page when sorting
    });
  };

  const setPage = (page: number) => {
    updateFilters({ page });
  };

  const setLimit = (limit: number) => {
    updateFilters({
      limit,
      page: 1 // Reset to first page when changing limit
    });
  };

  // Convenience functions for common operations
  const getActiveOrders = () => {
    return orders.filter(order => ['pending', 'preparing', 'ready'].includes(order.status));
  };

  const getCompletedOrders = () => {
    return orders.filter(order => ['completed', 'delivered'].includes(order.status));
  };

  const getCancelledOrders = () => {
    return orders.filter(order => order.status === 'cancelled');
  };

  const getOrdersByStatus = (status: string) => {
    return orders.filter(order => order.status === status);
  };

  const getOrdersByType = (orderType: string) => {
    return orders.filter(order => order.orderType === orderType);
  };

  return {
    // Data
    orders,
    pagination,
    orderStats,

    // State
    filters,
    isLoading,
    isError,
    error,

    // Actions
    updateFilters,
    clearFilters,
    setSearch,
    setStatus,
    setOrderType,
    setDateRange,
    setAmountRange,
    setSorting,
    setPage,
    setLimit,
    refetch,

    // Convenience functions
    getActiveOrders,
    getCompletedOrders,
    getCancelledOrders,
    getOrdersByStatus,
    getOrdersByType,
  };
}


