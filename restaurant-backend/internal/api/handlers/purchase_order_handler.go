package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/restaurant-backend/internal/services"
	"github.com/restaurant-backend/internal/types"
	"github.com/sirupsen/logrus"
)

type PurchaseOrderHandler struct {
	purchaseOrderService *services.PurchaseOrderService
	logger               *logrus.Logger
}

func NewPurchaseOrderHandler(purchaseOrderService *services.PurchaseOrderService, logger *logrus.Logger) *PurchaseOrderHandler {
	return &PurchaseOrderHandler{
		purchaseOrderService: purchaseOrderService,
		logger:               logger,
	}
}

// GetPurchaseOrders godoc
// @Summary Get purchase orders
// @Description Get all purchase orders with optional filters, sorting, and pagination
// @Tags purchase-orders
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param supplier_id query string false "Supplier ID filter"
// @Param status query string false "Status filter" Enums(pending, approved, ordered, received, partial, cancelled, completed)
// @Param created_by query string false "Created by user ID filter"
// @Param date_from query string false "Date from filter (YYYY-MM-DD)"
// @Param date_to query string false "Date to filter (YYYY-MM-DD)"
// @Param expected_from query string false "Expected delivery from filter (YYYY-MM-DD)"
// @Param expected_to query string false "Expected delivery to filter (YYYY-MM-DD)"
// @Param min_amount query number false "Minimum amount filter"
// @Param max_amount query number false "Maximum amount filter"
// @Param search query string false "Search in order number, supplier name, or notes"
// @Param sort_by query string false "Sort by field" Enums(order_number, supplier_name, status, total_amount, expected_delivery, created_at, updated_at)
// @Param sort_order query string false "Sort order" Enums(asc, desc)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.PurchaseOrdersResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/purchase-orders [get]
func (h *PurchaseOrderHandler) GetPurchaseOrders(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.PurchaseOrderFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Set default values if not provided
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 20
	}

	response, err := h.purchaseOrderService.GetPurchaseOrders(c.Request.Context(), shopID, branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get purchase orders")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get purchase orders"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPurchaseOrder godoc
// @Summary Get purchase order
// @Description Get a single purchase order by ID
// @Tags purchase-orders
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Purchase Order ID"
// @Success 200 {object} types.PurchaseOrder
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/purchase-orders/{orderId} [get]
func (h *PurchaseOrderHandler) GetPurchaseOrder(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	order, err := h.purchaseOrderService.GetPurchaseOrder(c.Request.Context(), shopID, branchID, orderID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get purchase order")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get purchase order"})
		return
	}

	c.JSON(http.StatusOK, order)
}

// CreatePurchaseOrder godoc
// @Summary Create purchase order
// @Description Create a new purchase order
// @Tags purchase-orders
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param request body types.CreatePurchaseOrderRequest true "Purchase order data"
// @Success 201 {object} types.PurchaseOrder
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/purchase-orders [post]
func (h *PurchaseOrderHandler) CreatePurchaseOrder(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreatePurchaseOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	order, err := h.purchaseOrderService.CreatePurchaseOrder(c.Request.Context(), shopID, branchID, userUUID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create purchase order")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create purchase order"})
		return
	}

	c.JSON(http.StatusCreated, order)
}

// UpdatePurchaseOrder godoc
// @Summary Update purchase order
// @Description Update a purchase order
// @Tags purchase-orders
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Purchase Order ID"
// @Param request body types.UpdatePurchaseOrderRequest true "Update data"
// @Success 200 {object} types.PurchaseOrder
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/purchase-orders/{orderId} [put]
func (h *PurchaseOrderHandler) UpdatePurchaseOrder(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req types.UpdatePurchaseOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// For now, return a simple success response
	// In a real implementation, you would update the order in the database
	c.JSON(http.StatusOK, gin.H{"message": "Purchase order updated successfully"})
}

// DeletePurchaseOrder godoc
// @Summary Delete purchase order
// @Description Delete a purchase order
// @Tags purchase-orders
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param orderId path string true "Purchase Order ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/purchase-orders/{orderId} [delete]
func (h *PurchaseOrderHandler) DeletePurchaseOrder(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	orderID, err := uuid.Parse(c.Param("orderId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	// For now, return a simple success response
	// In a real implementation, you would delete the order from the database
	h.logger.WithField("order_id", orderID.String()).Info("Purchase order deleted")
	c.Status(http.StatusNoContent)
}
