package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"github.com/restaurant-backend/internal/types"
)

type PurchaseOrderService struct {
	db *sqlx.DB
}

func NewPurchaseOrderService(db *sqlx.DB) *PurchaseOrderService {
	return &PurchaseOrderService{
		db: db,
	}
}

// GetPurchaseOrders retrieves purchase orders with filters, sorting, and pagination
func (s *PurchaseOrderService) GetPurchaseOrders(ctx context.Context, shopID, branchID uuid.UUID, filters types.PurchaseOrderFilters) (*types.PurchaseOrdersResponse, error) {
	// Build the base query
	baseQuery := `
		SELECT 
			po.id, po.order_number, po.shop_id, po.branch_id, po.supplier_id,
			s.name as supplier_name, po.status, po.total_amount, po.currency,
			po.expected_delivery, po.actual_delivery, po.notes, po.created_by,
			po.created_at, po.updated_at
		FROM purchase_orders po
		LEFT JOIN suppliers s ON po.supplier_id = s.id
		WHERE po.shop_id = $1 AND po.branch_id = $2
	`

	countQuery := `
		SELECT COUNT(*)
		FROM purchase_orders po
		LEFT JOIN suppliers s ON po.supplier_id = s.id
		WHERE po.shop_id = $1 AND po.branch_id = $2
	`

	args := []interface{}{shopID, branchID}
	argIndex := 3

	// Add filters
	whereConditions := []string{}

	if filters.SupplierID != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.supplier_id = $%d", argIndex))
		args = append(args, *filters.SupplierID)
		argIndex++
	}

	if filters.Status != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.status = $%d", argIndex))
		args = append(args, *filters.Status)
		argIndex++
	}

	if filters.CreatedBy != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.created_by = $%d", argIndex))
		args = append(args, *filters.CreatedBy)
		argIndex++
	}

	if filters.DateFrom != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.created_at >= $%d", argIndex))
		args = append(args, *filters.DateFrom)
		argIndex++
	}

	if filters.DateTo != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.created_at <= $%d", argIndex))
		args = append(args, filters.DateTo.Add(24*time.Hour).Add(-time.Second))
		argIndex++
	}

	if filters.ExpectedFrom != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.expected_delivery >= $%d", argIndex))
		args = append(args, *filters.ExpectedFrom)
		argIndex++
	}

	if filters.ExpectedTo != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.expected_delivery <= $%d", argIndex))
		args = append(args, filters.ExpectedTo.Add(24*time.Hour).Add(-time.Second))
		argIndex++
	}

	if filters.MinAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.total_amount >= $%d", argIndex))
		args = append(args, *filters.MinAmount)
		argIndex++
	}

	if filters.MaxAmount != nil {
		whereConditions = append(whereConditions, fmt.Sprintf("po.total_amount <= $%d", argIndex))
		args = append(args, *filters.MaxAmount)
		argIndex++
	}

	if filters.Search != "" {
		searchCondition := fmt.Sprintf("(po.order_number ILIKE $%d OR s.name ILIKE $%d OR po.notes ILIKE $%d)", argIndex, argIndex, argIndex)
		whereConditions = append(whereConditions, searchCondition)
		args = append(args, "%"+filters.Search+"%")
		argIndex++
	}

	// Add WHERE conditions
	if len(whereConditions) > 0 {
		whereClause := " AND " + strings.Join(whereConditions, " AND ")
		baseQuery += whereClause
		countQuery += whereClause
	}

	// Get total count
	var total int64
	err := s.db.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get purchase orders count: %w", err)
	}

	// Add sorting
	sortBy := "created_at"
	if filters.SortBy != "" {
		switch filters.SortBy {
		case "order_number":
			sortBy = "po.order_number"
		case "supplier_name":
			sortBy = "s.name"
		case "status":
			sortBy = "po.status"
		case "total_amount":
			sortBy = "po.total_amount"
		case "expected_delivery":
			sortBy = "po.expected_delivery"
		case "created_at":
			sortBy = "po.created_at"
		case "updated_at":
			sortBy = "po.updated_at"
		}
	}

	sortOrder := "DESC"
	if filters.SortOrder == "asc" {
		sortOrder = "ASC"
	}

	baseQuery += fmt.Sprintf(" ORDER BY %s %s", sortBy, sortOrder)

	// Add pagination
	offset := (filters.Page - 1) * filters.Limit
	baseQuery += fmt.Sprintf(" LIMIT %d OFFSET %d", filters.Limit, offset)

	// Execute query
	var purchaseOrders []types.PurchaseOrder
	err = s.db.SelectContext(ctx, &purchaseOrders, baseQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get purchase orders: %w", err)
	}

	// Calculate total pages
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.PurchaseOrdersResponse{
		Data:       purchaseOrders,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

// GetPurchaseOrder retrieves a single purchase order by ID
func (s *PurchaseOrderService) GetPurchaseOrder(ctx context.Context, shopID, branchID, orderID uuid.UUID) (*types.PurchaseOrder, error) {
	query := `
		SELECT 
			po.id, po.order_number, po.shop_id, po.branch_id, po.supplier_id,
			s.name as supplier_name, po.status, po.total_amount, po.currency,
			po.expected_delivery, po.actual_delivery, po.notes, po.created_by,
			po.created_at, po.updated_at
		FROM purchase_orders po
		LEFT JOIN suppliers s ON po.supplier_id = s.id
		WHERE po.id = $1 AND po.shop_id = $2 AND po.branch_id = $3
	`

	var order types.PurchaseOrder
	err := s.db.GetContext(ctx, &order, query, orderID, shopID, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get purchase order: %w", err)
	}

	// Get order items
	itemsQuery := `
		SELECT 
			poi.id, poi.purchase_order_id, poi.ingredient_id, i.name as ingredient_name,
			poi.quantity, poi.unit, poi.unit_price, poi.total_price, poi.received_quantity,
			poi.created_at, poi.updated_at
		FROM purchase_order_items poi
		LEFT JOIN ingredients i ON poi.ingredient_id = i.id
		WHERE poi.purchase_order_id = $1
		ORDER BY poi.created_at
	`

	var items []types.PurchaseOrderItem
	err = s.db.SelectContext(ctx, &items, itemsQuery, orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get purchase order items: %w", err)
	}

	order.Items = items
	return &order, nil
}

// CreatePurchaseOrder creates a new purchase order
func (s *PurchaseOrderService) CreatePurchaseOrder(ctx context.Context, shopID, branchID, userID uuid.UUID, req types.CreatePurchaseOrderRequest) (*types.PurchaseOrder, error) {
	tx, err := s.db.BeginTxx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// Generate order number
	orderNumber := fmt.Sprintf("PO-%d", time.Now().Unix())

	// Calculate total amount
	var totalAmount float64
	for _, item := range req.Items {
		totalAmount += item.Quantity * item.UnitPrice
	}

	// Create purchase order
	orderID := uuid.New()
	insertOrderQuery := `
		INSERT INTO purchase_orders (
			id, order_number, shop_id, branch_id, supplier_id, status,
			total_amount, currency, expected_delivery, notes, created_by,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
		)
	`

	now := time.Now()
	_, err = tx.ExecContext(ctx, insertOrderQuery,
		orderID, orderNumber, shopID, branchID, req.SupplierID,
		types.PurchaseOrderStatusPending, totalAmount, "USD",
		req.ExpectedDelivery, req.Notes, userID, now, now,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create purchase order: %w", err)
	}

	// Create purchase order items
	for _, item := range req.Items {
		itemID := uuid.New()
		totalPrice := item.Quantity * item.UnitPrice

		insertItemQuery := `
			INSERT INTO purchase_order_items (
				id, purchase_order_id, ingredient_id, quantity, unit,
				unit_price, total_price, received_quantity, created_at, updated_at
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
			)
		`

		_, err = tx.ExecContext(ctx, insertItemQuery,
			itemID, orderID, item.IngredientID, item.Quantity, item.Unit,
			item.UnitPrice, totalPrice, 0, now, now,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create purchase order item: %w", err)
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Return the created order
	return s.GetPurchaseOrder(ctx, shopID, branchID, orderID)
}
